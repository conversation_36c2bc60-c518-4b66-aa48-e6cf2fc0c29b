#!/usr/bin/env python3
"""
Feature Visualization Example

This example demonstrates how to use the feature visualization tools
with different types of models and datasets.
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def create_sample_images():
    """
    Create some sample synthetic images for demonstration
    """
    output_dir = Path("./sample_images")
    output_dir.mkdir(exist_ok=True)
    
    # Create different types of synthetic cell images
    samples = [
        ("circular_cell", create_circular_pattern),
        ("textured_cell", create_textured_pattern),
        ("complex_cell", create_complex_pattern),
    ]
    
    for name, pattern_func in samples:
        image = pattern_func()
        image_path = output_dir / f"{name}.png"
        image.save(image_path)
        print(f"Created sample image: {image_path}")
    
    return output_dir


def create_circular_pattern():
    """Create a circular cell-like pattern"""
    size = 224
    image = np.zeros((size, size, 3), dtype=np.uint8)

    # Create circular gradient
    center = size // 2
    y, x = np.ogrid[:size, :size]
    mask = (x - center) ** 2 + (y - center) ** 2 <= (size // 3) ** 2

    # Add some texture
    noise = np.random.randint(0, 50, (size, size))

    # Create cell-like structure
    image[mask, 0] = 150 + noise[mask]
    image[mask, 1] = 100 + noise[mask]
    image[mask, 2] = 180 + noise[mask]

    image[~mask, 0] = 50 + noise[~mask] // 2
    image[~mask, 1] = 50 + noise[~mask] // 2
    image[~mask, 2] = 50 + noise[~mask] // 2

    return Image.fromarray(image)


def create_textured_pattern():
    """Create a textured cell-like pattern"""
    size = 224
    image = np.zeros((size, size, 3), dtype=np.uint8)
    
    # Create texture using sine waves
    x = np.linspace(0, 4 * np.pi, size)
    y = np.linspace(0, 4 * np.pi, size)
    X, Y = np.meshgrid(x, y)
    
    texture = np.sin(X) * np.cos(Y) + np.sin(2 * X) * np.cos(3 * Y)
    texture = ((texture + 2) / 4 * 255).astype(np.uint8)
    
    # Create cell boundary
    center = size // 2
    y_coords, x_coords = np.ogrid[:size, :size]
    mask = (x_coords - center) ** 2 + (y_coords - center) ** 2 <= (size // 2.5) ** 2
    
    image[:, :, 0] = texture
    image[:, :, 1] = texture * 0.8
    image[:, :, 2] = texture * 1.2
    
    # Darken outside the cell
    image[~mask] = image[~mask] * 0.3
    
    return Image.fromarray(image)


def create_complex_pattern():
    """Create a complex multi-structure pattern"""
    size = 224
    image = np.zeros((size, size, 3), dtype=np.uint8)
    
    # Create multiple circular structures
    centers = [(size//3, size//3), (2*size//3, size//3), (size//2, 2*size//3)]
    radii = [size//8, size//10, size//12]
    
    for i, ((cx, cy), radius) in enumerate(zip(centers, radii)):
        y, x = np.ogrid[:size, :size]
        mask = (x - cx) ** 2 + (y - cy) ** 2 <= radius ** 2

        # Different colors for different structures
        colors = [(200, 100, 100), (100, 200, 100), (100, 100, 200)]
        color = colors[i]

        image[mask, 0] = color[0]
        image[mask, 1] = color[1]
        image[mask, 2] = color[2]
    
    # Add background texture
    noise = np.random.randint(30, 80, (size, size, 3))
    background_mask = np.all(image == 0, axis=2)
    image[background_mask] = noise[background_mask]
    
    return Image.fromarray(image)


def demonstrate_feature_extraction():
    """
    Demonstrate feature extraction without using the full framework
    """
    print("🔬 Demonstrating Feature Extraction")
    print("=" * 50)
    
    # Create sample images
    sample_dir = create_sample_images()
    
    # Check if we have a trained model
    model_paths = [
        "/jhcnas2/home/<USER>/CARE/checkpoints/cell_cls/ccs/cytofm/cytofm_All-IDB_*.pth",
        "./checkpoints/cell_cls/cytofm_*.pth",
        "./results/cell_cls/cytofm_*.pth"
    ]
    
    model_path = None
    for path_pattern in model_paths:
        import glob
        matches = glob.glob(path_pattern)
        if matches:
            model_path = matches[0]
            break
    
    if model_path and os.path.exists(model_path):
        print(f"Found model: {model_path}")
        
        # Run simple feature visualization
        cmd = f"""python tools/simple_feature_visualization.py \
            --model_path "{model_path}" \
            --image_dir "{sample_dir}" \
            --gpu 1 \
            --num_samples 3 \
            --num_channels 8 \
            --output_dir "./results/demo_features" """
        
        print(f"Running command: {cmd}")
        os.system(cmd)
        
    else:
        print("⚠️  No trained model found. Please train a model first or provide a model path.")
        print("Example model paths to check:")
        for path in model_paths:
            print(f"  - {path}")


def demonstrate_manual_feature_extraction():
    """
    Demonstrate manual feature extraction for educational purposes
    """
    print("\n🧠 Manual Feature Extraction Demo")
    print("=" * 50)
    
    # Create a simple synthetic image
    image = create_circular_pattern()
    
    # Convert to tensor
    image_array = np.array(image).astype(np.float32) / 255.0
    mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
    std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
    image_array = (image_array - mean) / std
    image_tensor = torch.from_numpy(image_array).permute(2, 0, 1).unsqueeze(0).float()

    # Simple feature extraction using convolution
    # This simulates what a CNN layer might do
    conv_filters = torch.randn(16, 3, 5, 5, dtype=torch.float32)  # 16 filters, 3 input channels, 5x5 kernel

    with torch.no_grad():
        features = torch.nn.functional.conv2d(image_tensor, conv_filters, padding=2)
    
    # Visualize the features
    fig, axes = plt.subplots(2, 8, figsize=(16, 4))
    
    for i in range(16):
        row = i // 8
        col = i % 8
        
        feature_map = features[0, i].numpy()
        feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min() + 1e-8)
        
        axes[row, col].imshow(feature_map, cmap='viridis')
        axes[row, col].set_title(f'Filter {i}')
        axes[row, col].axis('off')
    
    plt.suptitle('Manual Feature Extraction Demo\n(Simulated CNN Filters)', fontsize=14)
    plt.tight_layout()
    
    # Save the demo
    demo_dir = Path("./results/manual_demo")
    demo_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(demo_dir / "manual_feature_extraction.png", dpi=300, bbox_inches='tight')
    
    print(f"Manual demo saved to: {demo_dir / 'manual_feature_extraction.png'}")
    plt.show()


def print_usage_examples():
    """
    Print usage examples for the feature visualization tools
    """
    print("\n📚 Usage Examples")
    print("=" * 50)
    
    examples = [
        {
            "title": "Basic Feature Visualization",
            "description": "Visualize features from a trained model",
            "command": """python tools/simple_feature_visualization.py \\
    --model_path /path/to/model.pth \\
    --image_dir /path/to/images \\
    --gpu 1 \\
    --num_samples 5 \\
    --num_channels 12"""
        },
        {
            "title": "Full Framework Visualization",
            "description": "Use the complete framework with config files",
            "command": """python tools/feature_map_visualization.py \\
    --config configs/cell_cls/base_config.yaml \\
    --backbone cytofm \\
    --dataset All-IDB \\
    --gpu 1 \\
    --feature_layer all"""
        },
        {
            "title": "Batch Processing",
            "description": "Process multiple models and datasets",
            "command": """chmod +x scripts/run_feature_visualization.sh
./scripts/run_feature_visualization.sh"""
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   {example['description']}")
        print(f"   Command:")
        print(f"   {example['command']}")


def main():
    """
    Main demonstration function
    """
    print("🎯 Feature Map Visualization Demo")
    print("=" * 60)
    print("This demo shows how to use the feature visualization tools")
    print("for analyzing backbone models in the cell classification framework.")
    print()
    
    # Run demonstrations
    try:
        demonstrate_manual_feature_extraction()
        demonstrate_feature_extraction()
        print_usage_examples()
        
        print("\n✅ Demo completed successfully!")
        print("\nNext steps:")
        print("1. Train a model using the framework")
        print("2. Use the feature visualization tools to analyze the learned features")
        print("3. Compare different backbone architectures")
        print("4. Analyze attention patterns and feature importance")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
