# Quick test configuration
# Fast settings for testing and debugging

_base_: "base_config.yaml"

# Use lightweight model and fast settings
common:
  gpu: 4
backbone:
  name: "cytofm"
  freeze: true

data:
  dataset: "Herlev" 

training:
  batch_size: 32
  epochs: 50

evaluation:
  batch_size: 32
  compute_ci: true
  metrics:
    - "accuracy"
    - "auc"
    - "macro_f1"

output:
  model_dir: "/jhcnas2/home/<USER>/CARE/checkpoints/cell_cls/ccs/cytofm"
  results_dir: "results/cell_cls/cytofm"