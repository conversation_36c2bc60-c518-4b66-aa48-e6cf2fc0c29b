#!/usr/bin/env python3
"""
Simple Feature Map Visualization Tool

A simplified version that works with basic dependencies and focuses on DINOv2/DINOv3 models.
"""

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from PIL import Image
import glob
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Simple Feature Map Visualization Tool')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to trained model checkpoint')
    parser.add_argument('--image_dir', type=str, required=True,
                        help='Directory containing images to visualize')
    parser.add_argument('--gpu', type=int, default=0,
                        help='GPU device ID')
    parser.add_argument('--num_samples', type=int, default=5,
                        help='Number of samples to visualize')
    parser.add_argument('--output_dir', type=str, default='./results/simple_feature_maps',
                        help='Output directory for visualization results')
    parser.add_argument('--num_channels', type=int, default=12,
                        help='Number of feature channels to visualize')
    parser.add_argument('--image_size', type=int, default=224,
                        help='Input image size')
    return parser.parse_args()


def setup_device(gpu_id):
    """Setup device for computation"""
    if gpu_id is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: cuda:0 (physical GPU {gpu_id})")
    else:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
    return device


def load_simple_dinov2_model(model_path, device):
    """
    Load a simple DINOv2/DINOv3 model for feature extraction
    """
    try:
        # Try to load the model checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Extract model state dict
        if 'model' in checkpoint:
            state_dict = checkpoint['model']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # Try to import DINOv3 model
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backbone', 'dinov3'))
            from dinov3.models import vision_transformer as vits
            
            # Create model with typical DINOv3 parameters
            model = vits.vit_large(
                img_size=224,
                patch_size=16,
                pos_embed_rope_base=100,
                qkv_bias=True,
                layerscale_init=1.0e-05,
                norm_layer='layernorm',
                ffn_layer='mlp',
                ffn_bias=True,
                proj_bias=True,
                device=device
            )
            
            # Load state dict
            model.load_state_dict(state_dict, strict=False)
            model.to(device)
            model.eval()
            
            print(f"✅ Successfully loaded DINOv3 model from {model_path}")
            return model
            
        except Exception as e:
            print(f"Failed to load as DINOv3 model: {e}")
            return None
            
    except Exception as e:
        print(f"Error loading model: {e}")
        return None


def preprocess_image(image_path, image_size=224):
    """
    Preprocess image for model input
    """
    try:
        # Load and convert image
        image = Image.open(image_path).convert('RGB')
        
        # Resize image
        image = image.resize((image_size, image_size), Image.BILINEAR)
        
        # Convert to tensor and normalize
        image_array = np.array(image).astype(np.float32) / 255.0
        
        # ImageNet normalization
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        image_array = (image_array - mean) / std

        # Convert to tensor and add batch dimension
        image_tensor = torch.from_numpy(image_array).permute(2, 0, 1).unsqueeze(0).float()
        
        return image_tensor, image
        
    except Exception as e:
        print(f"Error preprocessing image {image_path}: {e}")
        return None, None


def extract_features(model, image_tensor, device):
    """
    Extract features from the model
    """
    feature_maps = {}
    
    def hook_fn(name):
        def hook(module, input, output):
            if isinstance(output, dict):
                if 'x_norm_patchtokens' in output:
                    feature_maps[name] = output['x_norm_patchtokens']
                elif 'x_norm_clstoken' in output:
                    feature_maps[name] = output['x_norm_clstoken']
            else:
                feature_maps[name] = output
        return hook
    
    # Register hooks
    hooks = []
    if hasattr(model, 'blocks') and len(model.blocks) > 0:
        # Last block
        hooks.append(model.blocks[-1].register_forward_hook(hook_fn('LastBlock')))
        # Middle block
        mid_idx = len(model.blocks) // 2
        hooks.append(model.blocks[mid_idx].register_forward_hook(hook_fn('MiddleBlock')))
    
    # Forward pass
    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        output = model(image_tensor)
        
        # If no hooks captured features, use output
        if not feature_maps and isinstance(output, dict):
            if 'x_norm_patchtokens' in output:
                feature_maps['Output'] = output['x_norm_patchtokens']
            elif 'x_norm_clstoken' in output:
                feature_maps['Output'] = output['x_norm_clstoken']
    
    # Remove hooks
    for hook in hooks:
        hook.remove()
    
    return feature_maps


def process_feature_map(feature_map, target_size=(224, 224)):
    """
    Process feature map for visualization
    """
    if len(feature_map.shape) == 3:
        # [batch, seq_len, embed_dim] -> [batch, embed_dim, H, W]
        batch_size, seq_len, embed_dim = feature_map.shape
        
        # Try to reshape to spatial dimensions (assuming 14x14 patches for 224x224 image)
        spatial_size = int(np.sqrt(seq_len))
        if spatial_size * spatial_size == seq_len:
            feature_map = feature_map.view(batch_size, spatial_size, spatial_size, embed_dim)
            feature_map = feature_map.permute(0, 3, 1, 2)  # [batch, embed_dim, H, W]
        else:
            # Fallback: create pseudo-spatial representation
            feature_map = feature_map.mean(dim=1, keepdim=True)  # [batch, 1, embed_dim]
            feature_map = feature_map.unsqueeze(-1).repeat(1, 1, 1, 14)
            feature_map = feature_map.view(batch_size, embed_dim, 1, 14)
            feature_map = F.interpolate(feature_map, size=(14, 14), mode='nearest')
    
    # Resize to target size
    if feature_map.shape[-2:] != target_size:
        feature_map = F.interpolate(feature_map, size=target_size, mode='bilinear', align_corners=False)
    
    return feature_map


def create_visualization(original_image, feature_maps, num_channels=12, save_path=None, sample_info=None):
    """
    Create feature map visualization
    """
    # Filter valid feature maps
    valid_feature_maps = {k: v for k, v in feature_maps.items() if v is not None}
    
    if not valid_feature_maps:
        print("No valid feature maps found!")
        return
    
    # Setup figure
    num_layers = len(valid_feature_maps)
    cols = min(num_channels, 6)
    rows = num_layers + 1
    
    fig = plt.figure(figsize=(cols * 3, rows * 2.5))
    gs = gridspec.GridSpec(rows, cols, figure=fig, hspace=0.4, wspace=0.3)
    
    # Plot original image
    ax_orig = fig.add_subplot(gs[0, :])
    ax_orig.imshow(original_image)
    title = 'Original Image'
    if sample_info:
        title += f" - {sample_info}"
    ax_orig.set_title(title, fontsize=14, fontweight='bold')
    ax_orig.axis('off')
    
    # Plot feature maps
    for layer_idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            processed_map = process_feature_map(feature_map)
            fmap_np = processed_map[0].cpu().numpy()  # [channels, height, width]
            
            # Select channels with highest variance
            num_available_channels = fmap_np.shape[0]
            if num_available_channels > cols:
                channel_vars = [(i, np.var(fmap_np[i])) for i in range(min(num_available_channels, 50))]
                channel_vars.sort(key=lambda x: x[1], reverse=True)
                channel_indices = [ch for ch, _ in channel_vars[:cols]]
            else:
                channel_indices = list(range(num_available_channels))
            
            row_idx = layer_idx + 1
            
            for col_idx, channel_idx in enumerate(channel_indices[:cols]):
                ax = fig.add_subplot(gs[row_idx, col_idx])
                
                # Extract and normalize channel
                channel_map = fmap_np[channel_idx]
                p1, p99 = np.percentile(channel_map, [1, 99])
                channel_map = np.clip(channel_map, p1, p99)
                if p99 > p1:
                    channel_map = (channel_map - p1) / (p99 - p1)
                
                # Plot
                im = ax.imshow(channel_map, cmap='viridis', aspect='auto')
                ax.set_title(f'{layer_name}\nCh {channel_idx}', fontsize=10)
                ax.axis('off')
                
                if col_idx == 0:
                    plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        
        except Exception as e:
            print(f"Error processing layer {layer_name}: {e}")
            continue
    
    plt.suptitle('Feature Map Visualization', fontsize=16, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Visualization saved to: {save_path}")
    
    plt.tight_layout()
    plt.show()
    plt.close()


def main():
    args = parse_args()
    
    # Setup device
    device = setup_device(args.gpu)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load model
    print(f"Loading model from: {args.model_path}")
    model = load_simple_dinov2_model(args.model_path, device)
    
    if model is None:
        print("❌ Failed to load model")
        return
    
    # Find images
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_paths = []
    for ext in image_extensions:
        image_paths.extend(glob.glob(os.path.join(args.image_dir, ext)))
        image_paths.extend(glob.glob(os.path.join(args.image_dir, ext.upper())))
    
    if not image_paths:
        print(f"❌ No images found in {args.image_dir}")
        return
    
    print(f"Found {len(image_paths)} images")
    
    # Process samples
    num_samples = min(args.num_samples, len(image_paths))
    print(f"Processing {num_samples} samples...")
    
    for i in range(num_samples):
        image_path = image_paths[i]
        print(f"\nProcessing sample {i+1}/{num_samples}: {os.path.basename(image_path)}")
        
        try:
            # Preprocess image
            image_tensor, original_image = preprocess_image(image_path, args.image_size)
            if image_tensor is None:
                continue
            
            # Extract features
            feature_maps = extract_features(model, image_tensor, device)
            
            # Create visualization
            sample_name = os.path.splitext(os.path.basename(image_path))[0]
            save_path = output_dir / f"sample_{i+1:03d}_{sample_name}_features.png"
            
            create_visualization(
                original_image=original_image,
                feature_maps=feature_maps,
                num_channels=args.num_channels,
                save_path=save_path,
                sample_info=f"Sample {i+1}: {sample_name}"
            )
            
            print(f"✅ Sample {i+1} completed")
            
        except Exception as e:
            print(f"❌ Error processing sample {i+1}: {e}")
            continue
    
    print(f"\n🎉 Feature visualization completed!")
    print(f"📁 Results saved to: {output_dir}")


if __name__ == '__main__':
    main()
