#!/usr/bin/env python3
"""
Feature Map Visualization Tool

This script visualizes feature maps extracted by backbone models and compares them with original images.
Supports various backbone models including ResNet, ViT, DINOv2, and pathology foundation models.
"""

import os
import sys
import argparse
import yaml
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from PIL import Image
import cv2
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from backbone.build_model import build_backbone
    from data.cell_cls.dataset import CellClsDataset
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed and the environment is activated.")
    sys.exit(1)


def create_feature_statistics_plot(feature_maps, save_path=None):
    """
    Create statistical analysis plots for feature maps

    Args:
        feature_maps: Dictionary of feature maps
        save_path: Path to save the statistics plot
    """
    valid_feature_maps = {k: v for k, v in feature_maps.items() if v is not None}

    if not valid_feature_maps:
        return

    num_layers = len(valid_feature_maps)
    fig, axes = plt.subplots(2, num_layers, figsize=(num_layers * 4, 8))

    if num_layers == 1:
        axes = axes.reshape(2, 1)

    for idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            # Process feature map
            processed_map = process_feature_map(feature_map)
            fmap_np = processed_map[0].cpu().numpy()  # [channels, height, width]

            # Calculate statistics across spatial dimensions
            channel_means = np.mean(fmap_np, axis=(1, 2))
            channel_stds = np.std(fmap_np, axis=(1, 2))

            # Plot 1: Channel activation distribution
            ax1 = axes[0, idx]
            ax1.hist(channel_means, bins=30, alpha=0.7, color='blue', edgecolor='black')
            ax1.set_title(f'{layer_name}\nChannel Mean Distribution')
            ax1.set_xlabel('Mean Activation')
            ax1.set_ylabel('Number of Channels')
            ax1.grid(True, alpha=0.3)

            # Plot 2: Feature map heatmap (average across channels)
            ax2 = axes[1, idx]
            avg_fmap = np.mean(fmap_np, axis=0)  # Average across channels
            im = ax2.imshow(avg_fmap, cmap='hot', aspect='auto')
            ax2.set_title(f'{layer_name}\nAverage Feature Map')
            ax2.axis('off')
            plt.colorbar(im, ax=ax2, fraction=0.046, pad=0.04)

        except Exception as e:
            print(f"Error creating statistics for {layer_name}: {e}")
            continue

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Feature statistics saved to: {save_path}")

    plt.show()
    plt.close()


def create_attention_map(feature_maps, original_image, save_path=None):
    """
    Create attention-like visualization by combining feature maps

    Args:
        feature_maps: Dictionary of feature maps
        original_image: Original input image
        save_path: Path to save the attention map
    """
    valid_feature_maps = {k: v for k, v in feature_maps.items() if v is not None}

    if not valid_feature_maps:
        return

    # Convert original image
    if isinstance(original_image, Image.Image):
        original_image = np.array(original_image)
    if original_image.max() > 1:
        original_image = original_image.astype(np.float32) / 255.0

    num_layers = len(valid_feature_maps)
    fig, axes = plt.subplots(1, num_layers + 1, figsize=((num_layers + 1) * 4, 4))

    if num_layers == 0:
        axes = [axes]

    # Plot original image
    axes[0].imshow(original_image)
    axes[0].set_title('Original Image')
    axes[0].axis('off')

    for idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            # Process feature map
            processed_map = process_feature_map(feature_map, target_size=original_image.shape[:2])
            fmap_np = processed_map[0].cpu().numpy()  # [channels, height, width]

            # Create attention map by taking the mean across channels and normalizing
            attention_map = np.mean(np.abs(fmap_np), axis=0)
            attention_map = (attention_map - attention_map.min()) / (attention_map.max() - attention_map.min() + 1e-8)

            # Overlay on original image
            ax = axes[idx + 1]
            ax.imshow(original_image)
            im = ax.imshow(attention_map, cmap='jet', alpha=0.6, aspect='auto')
            ax.set_title(f'{layer_name}\nAttention Map')
            ax.axis('off')
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

        except Exception as e:
            print(f"Error creating attention map for {layer_name}: {e}")
            continue

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Attention maps saved to: {save_path}")

    plt.show()
    plt.close()


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Feature Map Visualization Tool')
    parser.add_argument('--config', type=str, default='./configs/cell_cls/base_config.yaml',
                        help='Path to configuration file')
    parser.add_argument('--backbone', type=str, default='cytofm',
                        help='Backbone model name (e.g., cytofm, ResNet50, ViT-B-16)')
    parser.add_argument('--dataset', type=str, default='All-IDB',
                        help='Dataset name')
    parser.add_argument('--gpu', type=int, default=0,
                        help='GPU device ID')
    parser.add_argument('--num_samples', type=int, default=5,
                        help='Number of samples to visualize')
    parser.add_argument('--output_dir', type=str, default='./results/feature_maps',
                        help='Output directory for visualization results')
    parser.add_argument('--feature_layer', type=str, default='last',
                        help='Which layer to visualize (last, intermediate, all)')
    parser.add_argument('--num_channels', type=int, default=16,
                        help='Number of feature channels to visualize')
    return parser.parse_args()


def load_config(config_path):
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def setup_device(gpu_id):
    """Setup device for computation"""
    if gpu_id is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: cuda:0 (physical GPU {gpu_id})")
    else:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
    return device


def get_feature_maps(model, x, layer='last'):
    """
    Extract feature maps from the model

    Args:
        model: Backbone model
        x: Input tensor
        layer: Which layer to extract ('last', 'intermediate', 'all')

    Returns:
        feature_maps: Dictionary of feature maps
    """
    feature_maps = {}

    # Hook function to capture intermediate features
    def hook_fn(name):
        def hook(module, input, output):
            if isinstance(output, dict):
                # For DINOv2/DINOv3 models that return dict
                if 'x_norm_patchtokens' in output:
                    feature_maps[name] = output['x_norm_patchtokens']
                elif 'x_norm_clstoken' in output:
                    feature_maps[name] = output['x_norm_clstoken']
                else:
                    feature_maps[name] = list(output.values())[0] if output else None
            else:
                feature_maps[name] = output
        return hook

    # Register hooks based on model type
    hooks = []
    model_name = model.__class__.__name__.lower()

    # Check if it's a wrapped model (like DINOv2FeatureExtractor)
    if hasattr(model, 'backbone'):
        actual_model = model.backbone
        model_type = actual_model.__class__.__name__.lower()
    else:
        actual_model = model
        model_type = model_name

    print(f"Model type detected: {model_type}")

    if 'resnet' in model_type:
        # For ResNet models
        if layer in ['last', 'all']:
            if hasattr(actual_model, 'layer4'):
                hooks.append(actual_model.layer4.register_forward_hook(hook_fn('ResNet_Layer4')))
        if layer in ['intermediate', 'all']:
            if hasattr(actual_model, 'layer3'):
                hooks.append(actual_model.layer3.register_forward_hook(hook_fn('ResNet_Layer3')))
            if hasattr(actual_model, 'layer2'):
                hooks.append(actual_model.layer2.register_forward_hook(hook_fn('ResNet_Layer2')))

    elif 'vit' in model_type or 'dinov' in model_type or 'transformer' in model_type:
        # For ViT and DINOv2 models
        if hasattr(actual_model, 'blocks') and len(actual_model.blocks) > 0:
            # Register hook on the last few transformer blocks
            if layer in ['last', 'all']:
                hooks.append(actual_model.blocks[-1].register_forward_hook(hook_fn('Transformer_LastBlock')))
            if layer in ['intermediate', 'all']:
                mid_idx = len(actual_model.blocks) // 2
                hooks.append(actual_model.blocks[mid_idx].register_forward_hook(hook_fn('Transformer_MidBlock')))
                if len(actual_model.blocks) > 4:
                    early_idx = len(actual_model.blocks) // 4
                    hooks.append(actual_model.blocks[early_idx].register_forward_hook(hook_fn('Transformer_EarlyBlock')))

        # Also try to hook patch embedding
        if hasattr(actual_model, 'patch_embed'):
            hooks.append(actual_model.patch_embed.register_forward_hook(hook_fn('PatchEmbedding')))

    # Forward pass
    with torch.no_grad():
        output = model(x)

        # If no hooks captured features, use the final output
        if not feature_maps:
            if isinstance(output, dict):
                feature_maps['FinalOutput'] = output.get('x_norm_patchtokens',
                                                       output.get('x_norm_clstoken',
                                                                 list(output.values())[0] if output else output))
            else:
                feature_maps['FinalOutput'] = output

    # Remove hooks
    for hook in hooks:
        hook.remove()

    print(f"Extracted feature maps from layers: {list(feature_maps.keys())}")
    for name, fmap in feature_maps.items():
        if fmap is not None:
            print(f"  {name}: {fmap.shape}")

    return feature_maps


def process_feature_map(feature_map, target_size=(224, 224)):
    """
    Process feature map for visualization

    Args:
        feature_map: Raw feature map tensor
        target_size: Target size for resizing

    Returns:
        processed_map: Processed feature map ready for visualization
    """
    original_shape = feature_map.shape
    print(f"Processing feature map with shape: {original_shape}")

    if len(feature_map.shape) == 2:
        # For 2D features: [batch, features] -> [batch, 1, 1, features]
        feature_map = feature_map.unsqueeze(-1).unsqueeze(-1)
        feature_map = feature_map.expand(-1, -1, 14, 14)  # Expand to 14x14
        feature_map = feature_map.permute(0, 3, 1, 2)  # [batch, features, 14, 14]

    elif len(feature_map.shape) == 3:
        # For transformer models: [batch, seq_len, embed_dim]
        batch_size, seq_len, embed_dim = feature_map.shape

        # Try to reshape to spatial dimensions
        # Common cases: 196 = 14x14, 256 = 16x16, 64 = 8x8, etc.
        possible_sizes = [14, 16, 8, 7, 32, 28, 56]
        spatial_size = None

        for size in possible_sizes:
            if size * size == seq_len:
                spatial_size = size
                break

        if spatial_size is not None:
            # Reshape to spatial format
            feature_map = feature_map.view(batch_size, spatial_size, spatial_size, embed_dim)
            feature_map = feature_map.permute(0, 3, 1, 2)  # [batch, embed_dim, H, W]
        else:
            # If can't reshape to square, create a pseudo-spatial representation
            # Take the first N dimensions and arrange them in a grid
            max_spatial = int(np.sqrt(seq_len))
            if max_spatial > 0:
                truncated_seq = max_spatial * max_spatial
                feature_map = feature_map[:, :truncated_seq, :]
                feature_map = feature_map.view(batch_size, max_spatial, max_spatial, embed_dim)
                feature_map = feature_map.permute(0, 3, 1, 2)
            else:
                # Last resort: create a 1D representation
                feature_map = feature_map.mean(dim=1, keepdim=True)  # [batch, 1, embed_dim]
                feature_map = feature_map.unsqueeze(-1).repeat(1, 1, 1, 14)  # [batch, 1, embed_dim, 14]
                feature_map = feature_map.view(batch_size, embed_dim, 1, 14)
                feature_map = F.interpolate(feature_map, size=(14, 14), mode='nearest')

    elif len(feature_map.shape) == 4:
        # For CNN models: [batch, channels, height, width]
        pass

    elif len(feature_map.shape) == 1:
        # For 1D features: [features] -> [1, features, 1, 1]
        feature_map = feature_map.unsqueeze(0).unsqueeze(-1).unsqueeze(-1)
        feature_map = feature_map.expand(1, -1, 14, 14)

    else:
        raise ValueError(f"Unsupported feature map shape: {feature_map.shape}")

    # Ensure we have 4D tensor: [batch, channels, height, width]
    if len(feature_map.shape) != 4:
        raise ValueError(f"Failed to process feature map to 4D tensor. Current shape: {feature_map.shape}")

    # Resize to target size if needed
    current_size = feature_map.shape[-2:]
    if current_size != target_size:
        feature_map = F.interpolate(feature_map, size=target_size, mode='bilinear', align_corners=False)

    print(f"Processed feature map shape: {feature_map.shape}")
    return feature_map


def create_visualization(original_image, feature_maps, num_channels=16, save_path=None, sample_info=None):
    """
    Create visualization comparing original image with feature maps

    Args:
        original_image: Original input image (PIL Image or numpy array)
        feature_maps: Dictionary of feature maps
        num_channels: Number of feature channels to visualize
        save_path: Path to save the visualization
        sample_info: Dictionary with sample information (class, index, etc.)
    """
    # Convert original image to numpy array
    if isinstance(original_image, Image.Image):
        original_image = np.array(original_image)

    # Normalize original image to [0, 1]
    if original_image.max() > 1:
        original_image = original_image.astype(np.float32) / 255.0

    # Filter out None feature maps
    valid_feature_maps = {k: v for k, v in feature_maps.items() if v is not None}

    if not valid_feature_maps:
        print("No valid feature maps found!")
        return

    # Calculate grid size
    num_layers = len(valid_feature_maps)
    cols = min(num_channels, 6)  # Max 6 columns for better visibility
    rows = num_layers + 1  # +1 for original image row

    # Create figure with better sizing
    fig_width = max(12, cols * 2)
    fig_height = max(8, rows * 2.5)
    fig = plt.figure(figsize=(fig_width, fig_height))
    gs = gridspec.GridSpec(rows, cols + 1, figure=fig, hspace=0.4, wspace=0.3)

    # Create title with sample information
    title = 'Feature Map Visualization'
    if sample_info:
        title += f" - Sample {sample_info.get('index', '?')}"
        if 'class' in sample_info:
            title += f" (Class: {sample_info['class']})"

    # Plot original image
    ax_orig = fig.add_subplot(gs[0, :])
    ax_orig.imshow(original_image)
    ax_orig.set_title('Original Image', fontsize=14, fontweight='bold')
    ax_orig.axis('off')

    # Add image statistics
    img_stats = f"Shape: {original_image.shape}, Range: [{original_image.min():.3f}, {original_image.max():.3f}]"
    ax_orig.text(0.02, 0.02, img_stats, transform=ax_orig.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                fontsize=8, verticalalignment='bottom')

    # Plot feature maps for each layer
    for layer_idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            # Process feature map
            processed_map = process_feature_map(feature_map)

            # Get feature map statistics
            fmap_stats = {
                'shape': processed_map.shape,
                'mean': processed_map.mean().item(),
                'std': processed_map.std().item(),
                'min': processed_map.min().item(),
                'max': processed_map.max().item()
            }

            # Select channels to visualize
            num_available_channels = processed_map.shape[1]
            if num_available_channels == 0:
                continue

            # Use different strategies for channel selection
            if num_available_channels <= cols:
                channel_indices = list(range(num_available_channels))
            else:
                # Select channels with highest variance for better visualization
                channel_vars = []
                for ch in range(min(num_available_channels, 50)):  # Check first 50 channels
                    ch_map = processed_map[0, ch].cpu().numpy()
                    channel_vars.append((ch, np.var(ch_map)))

                # Sort by variance and select top channels
                channel_vars.sort(key=lambda x: x[1], reverse=True)
                channel_indices = [ch for ch, _ in channel_vars[:cols]]

            row_idx = layer_idx + 1

            for col_idx, channel_idx in enumerate(channel_indices):
                if col_idx >= cols:
                    break

                ax = fig.add_subplot(gs[row_idx, col_idx])

                # Extract and normalize channel
                channel_map = processed_map[0, channel_idx].cpu().numpy()

                # Robust normalization
                p1, p99 = np.percentile(channel_map, [1, 99])
                channel_map = np.clip(channel_map, p1, p99)
                if p99 > p1:
                    channel_map = (channel_map - p1) / (p99 - p1)
                else:
                    channel_map = np.zeros_like(channel_map)

                # Apply colormap
                im = ax.imshow(channel_map, cmap='viridis', aspect='auto')

                # Create title with channel info
                ch_mean = processed_map[0, channel_idx].mean().item()
                ch_std = processed_map[0, channel_idx].std().item()
                ax.set_title(f'{layer_name}\nCh {channel_idx}\n(μ={ch_mean:.2f}, σ={ch_std:.2f})',
                           fontsize=9)
                ax.axis('off')

                # Add colorbar for the first channel of each layer
                if col_idx == 0:
                    cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
                    cbar.ax.tick_params(labelsize=8)

            # Add layer statistics in the last column
            if cols < gs.ncols - 1:
                ax_stats = fig.add_subplot(gs[row_idx, -1])
                ax_stats.axis('off')
                stats_text = f"""Layer: {layer_name}
Shape: {fmap_stats['shape']}
Channels: {num_available_channels}
Mean: {fmap_stats['mean']:.3f}
Std: {fmap_stats['std']:.3f}
Range: [{fmap_stats['min']:.3f}, {fmap_stats['max']:.3f}]"""
                ax_stats.text(0.1, 0.5, stats_text, transform=ax_stats.transAxes,
                            fontsize=8, verticalalignment='center',
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))

        except Exception as e:
            print(f"Error processing layer {layer_name}: {e}")
            continue

    plt.suptitle(title, fontsize=16, fontweight='bold')

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Visualization saved to: {save_path}")

    plt.tight_layout()
    plt.show()
    plt.close()


def main():
    args = parse_args()
    
    # Setup device
    device = setup_device(args.gpu)
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    config['backbone']['name'] = args.backbone
    config['data']['dataset'] = args.dataset
    config['common']['gpu'] = args.gpu
    
    print(f"Backbone: {args.backbone}")
    print(f"Dataset: {args.dataset}")
    print(f"Device: {device}")
    
    # Build backbone model
    print("Building backbone model...")
    backbone, preprocess = build_backbone(config)
    backbone.eval()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Build dataset
    print("Loading dataset...")
    dataset = CellClsDataset(
        dataset_name=args.dataset,
        split='test',  # Use test split for visualization
        transform=preprocess
    )
    
    print(f"Dataset size: {len(dataset)}")
    print(f"Classes: {list(dataset.label_dict.keys())}")
    
    # Visualize samples
    num_samples = min(args.num_samples, len(dataset))
    print(f"Visualizing {num_samples} samples...")

    # Create summary statistics
    summary_stats = {
        'backbone': args.backbone,
        'dataset': args.dataset,
        'num_samples': num_samples,
        'feature_layer': args.feature_layer,
        'samples': []
    }

    for i in range(num_samples):
        print(f"\nProcessing sample {i+1}/{num_samples}...")

        try:
            # Get sample
            image, label = dataset[i]
            class_name = list(dataset.label_dict.keys())[label]

            # Load original image for visualization
            image_path = dataset.image_paths[i]
            original_image = Image.open(image_path).convert('RGB')

            print(f"  Sample info: Class={class_name}, Image path={image_path}")

            # Prepare input tensor
            input_tensor = image.unsqueeze(0).to(device)
            print(f"  Input tensor shape: {input_tensor.shape}")

            # Extract feature maps
            print(f"  Extracting feature maps...")
            feature_maps = get_feature_maps(backbone, input_tensor, layer=args.feature_layer)

            # Prepare sample info
            sample_info = {
                'index': i + 1,
                'class': class_name,
                'image_path': str(image_path)
            }

            # Create main visualization
            save_path = output_dir / f"sample_{i+1:03d}_{class_name}_{args.backbone}_{args.feature_layer}.png"
            print(f"  Creating main visualization...")
            create_visualization(
                original_image=original_image,
                feature_maps=feature_maps,
                num_channels=args.num_channels,
                save_path=save_path,
                sample_info=sample_info
            )

            # Create feature statistics plot
            stats_path = output_dir / f"sample_{i+1:03d}_{class_name}_{args.backbone}_{args.feature_layer}_stats.png"
            print(f"  Creating feature statistics...")
            create_feature_statistics_plot(feature_maps, save_path=stats_path)

            # Create attention maps
            attention_path = output_dir / f"sample_{i+1:03d}_{class_name}_{args.backbone}_{args.feature_layer}_attention.png"
            print(f"  Creating attention maps...")
            create_attention_map(feature_maps, original_image, save_path=attention_path)

            # Add to summary
            sample_summary = {
                'index': i + 1,
                'class': class_name,
                'image_path': str(image_path),
                'feature_maps': {name: {'shape': list(fmap.shape) if fmap is not None else None}
                               for name, fmap in feature_maps.items()},
                'visualization_path': str(save_path)
            }
            summary_stats['samples'].append(sample_summary)

            print(f"  ✅ Sample {i+1} completed successfully!")

        except Exception as e:
            print(f"  ❌ Error processing sample {i+1}: {e}")
            import traceback
            traceback.print_exc()
            continue

    # Save summary statistics
    summary_path = output_dir / f"visualization_summary_{args.backbone}_{args.feature_layer}.yaml"
    with open(summary_path, 'w') as f:
        yaml.dump(summary_stats, f, default_flow_style=False)

    print(f"\n🎉 Feature map visualization completed!")
    print(f"📁 Results saved to: {output_dir}")
    print(f"📊 Summary saved to: {summary_path}")
    print(f"✅ Successfully processed {len(summary_stats['samples'])} out of {num_samples} samples")


if __name__ == '__main__':
    main()
