#!/usr/bin/env python3
"""
Backbone-Specific Feature Visualization Tool

This tool provides feature visualization for different backbone models
including cytofm, ccs, gpfm, ResNet, ViT, etc.
"""

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from PIL import Image
import yaml
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from backbone.build_model import build_backbone
    from data.cell_cls.dataset import CellClsDataset
    FULL_FRAMEWORK_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Full framework not available: {e}")
    FULL_FRAMEWORK_AVAILABLE = False


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Backbone-Specific Feature Visualization')
    parser.add_argument('--backbone', type=str, required=True,
                        choices=['cytofm', 'ccs', 'gpfm', 'dinov2_vitl', 'ResNet50', 'ResNet18', 
                                'ViT-B-16', 'ViT-L-16', 'ViT-L-14', 'uni', 'conch', 'virchow'],
                        help='Backbone model name')
    parser.add_argument('--dataset', type=str, default='All-IDB',
                        choices=['All-IDB', 'SIPaKMeD', 'CERVIX93', 'Herlev'],
                        help='Dataset name (for loading data)')
    parser.add_argument('--config', type=str, default='./configs/cell_cls/base_config.yaml',
                        help='Path to config file')
    parser.add_argument('--gpu', type=int, default=1,
                        help='GPU device ID')
    parser.add_argument('--num_samples', type=int, default=5,
                        help='Number of samples to visualize')
    parser.add_argument('--output_dir', type=str, default='./results/backbone_features',
                        help='Output directory for visualization results')
    parser.add_argument('--num_channels', type=int, default=12,
                        help='Number of feature channels to visualize')
    parser.add_argument('--feature_layer', type=str, default='last',
                        choices=['last', 'intermediate', 'all'],
                        help='Which layers to extract features from')
    return parser.parse_args()


def setup_device_and_config(gpu_id, config_path):
    """Setup device and load configuration"""
    # Setup device
    if gpu_id is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: cuda:0 (physical GPU {gpu_id})")
    else:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
    
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    return device, config


def get_backbone_specific_features(backbone, input_tensor, layer_type='last'):
    """
    Extract features from backbone model with backbone-specific handling
    """
    feature_maps = {}
    hooks = []
    
    def hook_fn(name):
        def hook(module, input, output):
            if isinstance(output, dict):
                # Handle DINOv2/DINOv3 output format
                if 'x_norm_patchtokens' in output:
                    feature_maps[name] = output['x_norm_patchtokens']
                elif 'x_norm_clstoken' in output:
                    feature_maps[name] = output['x_norm_clstoken']
                else:
                    # Take the first available output
                    for key, value in output.items():
                        if torch.is_tensor(value):
                            feature_maps[name] = value
                            break
            elif isinstance(output, (list, tuple)):
                # Handle list/tuple outputs
                for i, item in enumerate(output):
                    if torch.is_tensor(item):
                        feature_maps[f"{name}_item_{i}"] = item
            else:
                # Handle tensor outputs
                feature_maps[name] = output
        return hook
    
    # Get the actual model (handle wrapped models)
    if hasattr(backbone, 'backbone'):
        actual_model = backbone.backbone
        model_name = actual_model.__class__.__name__.lower()
    else:
        actual_model = backbone
        model_name = backbone.__class__.__name__.lower()
    
    print(f"Extracting features from {model_name} backbone")
    
    # Register hooks based on model architecture
    if 'resnet' in model_name:
        # ResNet models
        if layer_type in ['last', 'all'] and hasattr(actual_model, 'layer4'):
            hooks.append(actual_model.layer4.register_forward_hook(hook_fn('ResNet_Layer4')))
        if layer_type in ['intermediate', 'all']:
            if hasattr(actual_model, 'layer3'):
                hooks.append(actual_model.layer3.register_forward_hook(hook_fn('ResNet_Layer3')))
            if hasattr(actual_model, 'layer2'):
                hooks.append(actual_model.layer2.register_forward_hook(hook_fn('ResNet_Layer2')))
    
    elif any(x in model_name for x in ['vit', 'dinov', 'transformer']):
        # Vision Transformer models (ViT, DINOv2, DINOv3)
        if hasattr(actual_model, 'blocks') and len(actual_model.blocks) > 0:
            if layer_type in ['last', 'all']:
                hooks.append(actual_model.blocks[-1].register_forward_hook(hook_fn('Transformer_LastBlock')))
            if layer_type in ['intermediate', 'all']:
                mid_idx = len(actual_model.blocks) // 2
                hooks.append(actual_model.blocks[mid_idx].register_forward_hook(hook_fn('Transformer_MidBlock')))
                if len(actual_model.blocks) > 6:
                    early_idx = len(actual_model.blocks) // 4
                    hooks.append(actual_model.blocks[early_idx].register_forward_hook(hook_fn('Transformer_EarlyBlock')))
        
        # Also hook patch embedding if available
        if hasattr(actual_model, 'patch_embed'):
            hooks.append(actual_model.patch_embed.register_forward_hook(hook_fn('PatchEmbedding')))
    
    # Forward pass
    with torch.no_grad():
        try:
            output = backbone(input_tensor)
            
            # If no hooks captured features, try to use the output
            if not feature_maps:
                if isinstance(output, dict):
                    for key, value in output.items():
                        if torch.is_tensor(value):
                            feature_maps[f'Output_{key}'] = value
                elif torch.is_tensor(output):
                    feature_maps['Output'] = output
                    
        except Exception as e:
            print(f"Error during forward pass: {e}")
    
    # Remove hooks
    for hook in hooks:
        hook.remove()
    
    print(f"Extracted {len(feature_maps)} feature maps: {list(feature_maps.keys())}")
    return feature_maps


def process_feature_for_visualization(feature_map, target_size=(224, 224)):
    """
    Process feature map for visualization with robust handling
    """
    if feature_map is None:
        return None
    
    original_shape = feature_map.shape
    print(f"  Processing feature shape: {original_shape}")
    
    # Handle different tensor shapes
    if len(feature_map.shape) == 2:
        # [batch, features] -> expand to spatial
        feature_map = feature_map.unsqueeze(-1).unsqueeze(-1)
        feature_map = feature_map.expand(-1, -1, 14, 14)
        feature_map = feature_map.permute(0, 3, 1, 2)
    
    elif len(feature_map.shape) == 3:
        # [batch, seq_len, embed_dim] -> [batch, embed_dim, H, W]
        batch_size, seq_len, embed_dim = feature_map.shape
        
        # Try common spatial sizes
        spatial_sizes = [14, 16, 8, 7, 28, 32, 56]
        spatial_size = None
        
        for size in spatial_sizes:
            if size * size == seq_len:
                spatial_size = size
                break
        
        if spatial_size:
            feature_map = feature_map.view(batch_size, spatial_size, spatial_size, embed_dim)
            feature_map = feature_map.permute(0, 3, 1, 2)
        else:
            # Fallback: use first sqrt(seq_len) tokens
            approx_size = int(np.sqrt(seq_len))
            if approx_size > 0:
                truncated_len = approx_size * approx_size
                feature_map = feature_map[:, :truncated_len, :]
                feature_map = feature_map.view(batch_size, approx_size, approx_size, embed_dim)
                feature_map = feature_map.permute(0, 3, 1, 2)
            else:
                # Last resort
                feature_map = feature_map.mean(dim=1, keepdim=True).unsqueeze(-1).unsqueeze(-1)
                feature_map = feature_map.expand(-1, -1, 14, 14)
    
    elif len(feature_map.shape) == 4:
        # Already in [batch, channels, height, width] format
        pass
    
    else:
        print(f"  Unsupported feature shape: {feature_map.shape}")
        return None
    
    # Resize to target size
    if feature_map.shape[-2:] != target_size:
        feature_map = F.interpolate(feature_map, size=target_size, mode='bilinear', align_corners=False)
    
    print(f"  Final processed shape: {feature_map.shape}")
    return feature_map


def create_backbone_visualization(original_image, feature_maps, backbone_name, num_channels=12, save_path=None):
    """
    Create backbone-specific feature visualization
    """
    valid_feature_maps = {k: v for k, v in feature_maps.items() 
                         if v is not None and torch.is_tensor(v)}
    
    if not valid_feature_maps:
        print("No valid feature maps found!")
        return
    
    # Setup figure
    num_layers = len(valid_feature_maps)
    cols = min(num_channels, 6)
    rows = num_layers + 1
    
    fig = plt.figure(figsize=(cols * 3, rows * 2.5))
    gs = gridspec.GridSpec(rows, cols + 1, figure=fig, hspace=0.4, wspace=0.3)
    
    # Plot original image
    ax_orig = fig.add_subplot(gs[0, :])
    if isinstance(original_image, Image.Image):
        original_image = np.array(original_image)
    ax_orig.imshow(original_image)
    ax_orig.set_title(f'Original Image - {backbone_name} Backbone', fontsize=14, fontweight='bold')
    ax_orig.axis('off')
    
    # Plot feature maps
    for layer_idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            processed_map = process_feature_for_visualization(feature_map)
            if processed_map is None:
                continue
                
            fmap_np = processed_map[0].cpu().numpy()
            
            # Select most informative channels
            num_available = fmap_np.shape[0]
            if num_available > cols:
                # Select channels with highest variance
                variances = [np.var(fmap_np[i]) for i in range(min(num_available, 50))]
                top_indices = np.argsort(variances)[-cols:][::-1]
            else:
                top_indices = list(range(num_available))
            
            row_idx = layer_idx + 1
            
            for col_idx, ch_idx in enumerate(top_indices[:cols]):
                ax = fig.add_subplot(gs[row_idx, col_idx])
                
                # Normalize channel
                ch_map = fmap_np[ch_idx]
                p1, p99 = np.percentile(ch_map, [5, 95])
                ch_map = np.clip(ch_map, p1, p99)
                if p99 > p1:
                    ch_map = (ch_map - p1) / (p99 - p1)
                
                im = ax.imshow(ch_map, cmap='viridis', aspect='auto')
                ax.set_title(f'{layer_name}\nCh {ch_idx}', fontsize=9)
                ax.axis('off')
                
                if col_idx == 0:
                    plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            
            # Add statistics
            if cols < gs.ncols - 1:
                ax_stats = fig.add_subplot(gs[row_idx, -1])
                ax_stats.axis('off')
                stats_text = f"""Layer: {layer_name}
Shape: {processed_map.shape}
Channels: {num_available}
Mean: {processed_map.mean().item():.3f}
Std: {processed_map.std().item():.3f}"""
                ax_stats.text(0.1, 0.5, stats_text, transform=ax_stats.transAxes,
                            fontsize=8, verticalalignment='center',
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
        except Exception as e:
            print(f"Error visualizing {layer_name}: {e}")
            continue
    
    plt.suptitle(f'{backbone_name} Backbone Feature Maps', fontsize=16, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Visualization saved to: {save_path}")
    
    plt.tight_layout()
    plt.show()
    plt.close()


def main():
    args = parse_args()
    
    # Setup device and config
    device, config = setup_device_and_config(args.gpu, args.config)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"🔬 Backbone Feature Visualization")
    print(f"Backbone: {args.backbone}")
    print(f"Dataset: {args.dataset}")
    print(f"Feature layer: {args.feature_layer}")
    print(f"Output directory: {output_dir}")
    print()
    
    if not FULL_FRAMEWORK_AVAILABLE:
        print("❌ Full framework not available. Please ensure all dependencies are installed.")
        return
    
    try:
        # Update config for the specific backbone and dataset
        config['backbone']['name'] = args.backbone
        config['data']['dataset'] = args.dataset
        config['common']['gpu'] = args.gpu
        
        # Build backbone model
        print(f"Building {args.backbone} backbone...")
        backbone = build_backbone(config)
        backbone.eval()
        
        # Load dataset
        print(f"Loading {args.dataset} dataset...")
        dataset = CellClsDataset(config, split='test')
        
        # Process samples
        num_samples = min(args.num_samples, len(dataset))
        print(f"Processing {num_samples} samples...")
        
        for i in range(num_samples):
            print(f"\nSample {i+1}/{num_samples}")
            
            # Get sample
            image_tensor, label = dataset[i]
            class_name = list(dataset.label_dict.keys())[label]
            
            # Load original image
            image_path = dataset.image_paths[i]
            original_image = Image.open(image_path).convert('RGB')
            
            print(f"  Class: {class_name}")
            print(f"  Image: {os.path.basename(image_path)}")
            
            # Prepare input
            input_tensor = image_tensor.unsqueeze(0).to(device)
            
            # Extract features
            feature_maps = get_backbone_specific_features(backbone, input_tensor, args.feature_layer)
            
            # Create visualization
            save_path = output_dir / f"{args.backbone}_{args.dataset}_sample_{i+1:03d}_{class_name}_{args.feature_layer}.png"
            create_backbone_visualization(
                original_image=original_image,
                feature_maps=feature_maps,
                backbone_name=args.backbone,
                num_channels=args.num_channels,
                save_path=save_path
            )
            
            print(f"  ✅ Completed sample {i+1}")
        
        print(f"\n🎉 Backbone feature visualization completed!")
        print(f"📁 Results saved to: {output_dir}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
