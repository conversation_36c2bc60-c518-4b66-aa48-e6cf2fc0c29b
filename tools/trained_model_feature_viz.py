#!/usr/bin/env python3
"""
Trained Model Feature Visualization

This tool visualizes features from already trained models without requiring
the full framework dependencies.
"""

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from PIL import Image
import glob
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Trained Model Feature Visualization')
    parser.add_argument('--backbone', type=str, required=True,
                        choices=['cytofm', 'ccs', 'gpfm', 'dinov2_vitl'],
                        help='Backbone model name')
    parser.add_argument('--image_dir', type=str, required=True,
                        help='Directory containing images to visualize')
    parser.add_argument('--gpu', type=int, default=1,
                        help='GPU device ID')
    parser.add_argument('--num_samples', type=int, default=5,
                        help='Number of samples to visualize')
    parser.add_argument('--output_dir', type=str, default='./results/trained_model_features',
                        help='Output directory for visualization results')
    parser.add_argument('--num_channels', type=int, default=12,
                        help='Number of feature channels to visualize')
    parser.add_argument('--image_size', type=int, default=224,
                        help='Input image size')
    return parser.parse_args()


def setup_device(gpu_id):
    """Setup device for computation"""
    if gpu_id is not None:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: cuda:0 (physical GPU {gpu_id})")
    else:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
    return device


def get_model_path(backbone_name):
    """Get the model path for the specified backbone"""
    model_paths = {
        'cytofm': '/jhcnas2/home/<USER>/CARE/SSL/superpod/vitl-16-dinov3.pth',
        'ccs': '/jhcnas4/Cervical/Smart-CCS_Experiment_log/superpod/ckpts/100M/vitl_3.pth',
        'gpfm': '/jhcnas3/Pathology/code/PrePath/models/ckpts/GPFM.pth',
        'dinov2_vitl': '/path/to/dinov2_vitl.pth'  # Update this path as needed
    }
    
    if backbone_name in model_paths:
        model_path = model_paths[backbone_name]
        if os.path.exists(model_path):
            return model_path
        else:
            print(f"⚠️  Model path not found: {model_path}")
    
    # Try to find trained models in checkpoints
    checkpoint_patterns = [
        f"./checkpoints/cell_cls/*{backbone_name}*.pth",
        f"./results/cell_cls/*{backbone_name}*.pth",
        f"/jhcnas2/home/<USER>/CARE/checkpoints/cell_cls/*{backbone_name}*.pth"
    ]
    
    for pattern in checkpoint_patterns:
        matches = glob.glob(pattern)
        if matches:
            return matches[0]
    
    return None


def load_dinov3_model(model_path, device):
    """Load DINOv3 model"""
    try:
        # Add DINOv3 path
        dinov3_path = os.path.join(os.path.dirname(__file__), '..', 'backbone', 'dinov3')
        if dinov3_path not in sys.path:
            sys.path.append(dinov3_path)
        
        from dinov3.models import vision_transformer as vits
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Extract state dict
        if 'model' in checkpoint:
            state_dict = checkpoint['model']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # Create model
        model = vits.vit_large(
            img_size=224,
            patch_size=16,
            pos_embed_rope_base=100,
            qkv_bias=True,
            layerscale_init=1.0e-05,
            norm_layer='layernorm',
            ffn_layer='mlp',
            ffn_bias=True,
            proj_bias=True,
            device=device
        )
        
        # Load weights
        model.load_state_dict(state_dict, strict=False)
        model.to(device)
        model.eval()
        
        print(f"✅ Successfully loaded DINOv3 model from {model_path}")
        return model
        
    except Exception as e:
        print(f"❌ Failed to load DINOv3 model: {e}")
        return None


def preprocess_image(image_path, image_size=224):
    """Preprocess image for model input"""
    try:
        # Load image
        image = Image.open(image_path).convert('RGB')
        original_image = image.copy()
        
        # Resize
        image = image.resize((image_size, image_size), Image.BILINEAR)
        
        # Convert to tensor and normalize
        image_array = np.array(image).astype(np.float32) / 255.0
        
        # ImageNet normalization
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        image_array = (image_array - mean) / std
        
        # Convert to tensor
        image_tensor = torch.from_numpy(image_array).permute(2, 0, 1).unsqueeze(0).float()
        
        return image_tensor, original_image
        
    except Exception as e:
        print(f"Error preprocessing {image_path}: {e}")
        return None, None


def extract_backbone_features(model, image_tensor, device, backbone_name):
    """Extract features from backbone model"""
    feature_maps = {}
    hooks = []
    
    def hook_fn(name):
        def hook(module, input, output):
            if isinstance(output, dict):
                # DINOv3 output format
                if 'x_norm_patchtokens' in output:
                    feature_maps[name] = output['x_norm_patchtokens'].clone()
                elif 'x_norm_clstoken' in output:
                    feature_maps[name] = output['x_norm_clstoken'].clone()
                else:
                    # Take first tensor output
                    for key, value in output.items():
                        if torch.is_tensor(value):
                            feature_maps[name] = value.clone()
                            break
            elif torch.is_tensor(output):
                feature_maps[name] = output.clone()
        return hook
    
    # Register hooks based on backbone
    if backbone_name in ['cytofm', 'ccs', 'gpfm', 'dinov2_vitl']:
        # Vision Transformer models
        if hasattr(model, 'blocks') and len(model.blocks) > 0:
            # Last block
            hooks.append(model.blocks[-1].register_forward_hook(hook_fn('LastBlock')))
            # Middle block
            mid_idx = len(model.blocks) // 2
            hooks.append(model.blocks[mid_idx].register_forward_hook(hook_fn('MiddleBlock')))
            # Early block
            if len(model.blocks) > 6:
                early_idx = len(model.blocks) // 4
                hooks.append(model.blocks[early_idx].register_forward_hook(hook_fn('EarlyBlock')))
        
        # Patch embedding
        if hasattr(model, 'patch_embed'):
            hooks.append(model.patch_embed.register_forward_hook(hook_fn('PatchEmbedding')))
    
    # Forward pass
    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        try:
            output = model(image_tensor)
            
            # If no hooks captured features, use output
            if not feature_maps and isinstance(output, dict):
                if 'x_norm_patchtokens' in output:
                    feature_maps['ModelOutput'] = output['x_norm_patchtokens']
                elif 'x_norm_clstoken' in output:
                    feature_maps['ModelOutput'] = output['x_norm_clstoken']
        except Exception as e:
            print(f"Error in forward pass: {e}")
    
    # Remove hooks
    for hook in hooks:
        hook.remove()
    
    print(f"Extracted features from {len(feature_maps)} layers: {list(feature_maps.keys())}")
    return feature_maps


def process_feature_map(feature_map, target_size=(224, 224)):
    """Process feature map for visualization"""
    if feature_map is None or not torch.is_tensor(feature_map):
        return None
    
    original_shape = feature_map.shape
    
    if len(feature_map.shape) == 3:
        # [batch, seq_len, embed_dim] -> [batch, embed_dim, H, W]
        batch_size, seq_len, embed_dim = feature_map.shape
        
        # Try to reshape to spatial dimensions
        spatial_size = int(np.sqrt(seq_len))
        if spatial_size * spatial_size == seq_len:
            feature_map = feature_map.view(batch_size, spatial_size, spatial_size, embed_dim)
            feature_map = feature_map.permute(0, 3, 1, 2)
        else:
            # Fallback: create pseudo-spatial representation
            approx_size = max(1, int(np.sqrt(seq_len)))
            truncated_len = approx_size * approx_size
            if truncated_len <= seq_len:
                feature_map = feature_map[:, :truncated_len, :]
                feature_map = feature_map.view(batch_size, approx_size, approx_size, embed_dim)
                feature_map = feature_map.permute(0, 3, 1, 2)
            else:
                # Last resort
                feature_map = feature_map.mean(dim=1, keepdim=True).unsqueeze(-1).unsqueeze(-1)
                feature_map = feature_map.expand(-1, -1, 14, 14)
    
    elif len(feature_map.shape) == 4:
        # Already in correct format
        pass
    
    elif len(feature_map.shape) == 2:
        # [batch, features] -> expand
        feature_map = feature_map.unsqueeze(-1).unsqueeze(-1)
        feature_map = feature_map.expand(-1, -1, 14, 14)
    
    else:
        print(f"Unsupported feature shape: {original_shape}")
        return None
    
    # Resize to target size
    if feature_map.shape[-2:] != target_size:
        feature_map = F.interpolate(feature_map, size=target_size, mode='bilinear', align_corners=False)
    
    return feature_map


def create_backbone_feature_visualization(original_image, feature_maps, backbone_name, num_channels=12, save_path=None, sample_info=None):
    """Create backbone-specific feature visualization"""
    valid_feature_maps = {k: v for k, v in feature_maps.items() if v is not None}
    
    if not valid_feature_maps:
        print("No valid feature maps found!")
        return
    
    # Setup figure
    num_layers = len(valid_feature_maps)
    cols = min(num_channels, 6)
    rows = num_layers + 1
    
    fig = plt.figure(figsize=(cols * 3, rows * 2.5))
    gs = gridspec.GridSpec(rows, cols + 1, figure=fig, hspace=0.4, wspace=0.3)
    
    # Plot original image
    ax_orig = fig.add_subplot(gs[0, :])
    if isinstance(original_image, Image.Image):
        original_image = np.array(original_image)
    ax_orig.imshow(original_image)
    
    title = f'Original Image - {backbone_name.upper()} Backbone'
    if sample_info:
        title += f' - {sample_info}'
    ax_orig.set_title(title, fontsize=14, fontweight='bold')
    ax_orig.axis('off')
    
    # Plot feature maps
    for layer_idx, (layer_name, feature_map) in enumerate(valid_feature_maps.items()):
        try:
            processed_map = process_feature_map(feature_map)
            if processed_map is None:
                continue
            
            fmap_np = processed_map[0].cpu().numpy()
            num_available = fmap_np.shape[0]
            
            # Select most informative channels
            if num_available > cols:
                variances = []
                for i in range(min(num_available, 50)):
                    var = np.var(fmap_np[i])
                    variances.append((i, var))
                variances.sort(key=lambda x: x[1], reverse=True)
                channel_indices = [idx for idx, _ in variances[:cols]]
            else:
                channel_indices = list(range(num_available))
            
            row_idx = layer_idx + 1
            
            for col_idx, ch_idx in enumerate(channel_indices[:cols]):
                ax = fig.add_subplot(gs[row_idx, col_idx])
                
                # Normalize channel
                ch_map = fmap_np[ch_idx]
                p5, p95 = np.percentile(ch_map, [5, 95])
                ch_map = np.clip(ch_map, p5, p95)
                if p95 > p5:
                    ch_map = (ch_map - p5) / (p95 - p5)
                
                im = ax.imshow(ch_map, cmap='viridis', aspect='auto')
                ax.set_title(f'{layer_name}\nCh {ch_idx}', fontsize=9)
                ax.axis('off')
                
                if col_idx == 0:
                    plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            
            # Add layer statistics
            if cols < gs.ncols - 1:
                ax_stats = fig.add_subplot(gs[row_idx, -1])
                ax_stats.axis('off')
                stats_text = f"""Layer: {layer_name}
Shape: {processed_map.shape}
Channels: {num_available}
Mean: {processed_map.mean().item():.3f}
Std: {processed_map.std().item():.3f}"""
                ax_stats.text(0.1, 0.5, stats_text, transform=ax_stats.transAxes,
                            fontsize=8, verticalalignment='center',
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))
        
        except Exception as e:
            print(f"Error visualizing {layer_name}: {e}")
            continue
    
    plt.suptitle(f'{backbone_name.upper()} Backbone Feature Maps', fontsize=16, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"Visualization saved to: {save_path}")
    
    plt.tight_layout()
    plt.show()
    plt.close()


def main():
    args = parse_args()
    
    # Setup device
    device = setup_device(args.gpu)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"🔬 {args.backbone.upper()} Backbone Feature Visualization")
    print(f"Image directory: {args.image_dir}")
    print(f"Output directory: {output_dir}")
    print()
    
    # Get model path
    model_path = get_model_path(args.backbone)
    if model_path is None:
        print(f"❌ No model found for backbone: {args.backbone}")
        return
    
    print(f"Using model: {model_path}")
    
    # Load model
    model = load_dinov3_model(model_path, device)
    if model is None:
        print("❌ Failed to load model")
        return
    
    # Find images
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    image_paths = []
    for ext in image_extensions:
        image_paths.extend(glob.glob(os.path.join(args.image_dir, ext)))
        image_paths.extend(glob.glob(os.path.join(args.image_dir, ext.upper())))
    
    if not image_paths:
        print(f"❌ No images found in {args.image_dir}")
        return
    
    print(f"Found {len(image_paths)} images")
    
    # Process samples
    num_samples = min(args.num_samples, len(image_paths))
    print(f"Processing {num_samples} samples...")
    
    for i in range(num_samples):
        image_path = image_paths[i]
        sample_name = os.path.splitext(os.path.basename(image_path))[0]
        print(f"\nProcessing sample {i+1}/{num_samples}: {sample_name}")
        
        try:
            # Preprocess image
            image_tensor, original_image = preprocess_image(image_path, args.image_size)
            if image_tensor is None:
                continue
            
            # Extract features
            feature_maps = extract_backbone_features(model, image_tensor, device, args.backbone)
            
            # Create visualization
            save_path = output_dir / f"{args.backbone}_sample_{i+1:03d}_{sample_name}_features.png"
            create_backbone_feature_visualization(
                original_image=original_image,
                feature_maps=feature_maps,
                backbone_name=args.backbone,
                num_channels=args.num_channels,
                save_path=save_path,
                sample_info=f"Sample {i+1}: {sample_name}"
            )
            
            print(f"✅ Sample {i+1} completed")
            
        except Exception as e:
            print(f"❌ Error processing sample {i+1}: {e}")
            continue
    
    print(f"\n🎉 {args.backbone.upper()} backbone feature visualization completed!")
    print(f"📁 Results saved to: {output_dir}")


if __name__ == '__main__':
    main()
