# Prefer DINOv3 vision_transformer definitions for vits
try:
    from dinov3.models import vision_transformer as vits
except Exception:
    import os, sys
    sys.path.append(os.path.join(os.path.dirname(__file__), "..", "dinov3"))
    from dinov3.models import vision_transformer as vits
import torch
from torchvision import transforms


def build_model(device, gpu_num, model_name, ckpt_path):
    print("Loading backbone ckpt:", model_name)
    if model_name in ['dinov2_vitl']:
        vit_kwargs = dict(
            img_size=224,
            patch_size=14,
            layerscale_init=1.0e-05,
            # DINOv3 supports 'mlp' and 'swiglu' families
            ffn_layer='swiglu',
            qkv_bias=True,
            proj_bias=True,
            ffn_bias=True,
        )
        teacher = vits.__dict__['vit_large'](**vit_kwargs)
        
    elif model_name.lower() in ['gpfm','ccs']:
        vit_kwargs = dict(
            img_size=224,
            patch_size=14,
            layerscale_init=1.0e-05,
            ffn_layer='mlp',
            qkv_bias=True,
            proj_bias=True,
            ffn_bias=True,
        )
        teacher = vits.__dict__['vit_large'](**vit_kwargs)

    elif model_name.lower() in ['cytofm']:
        vit_kwargs = dict(
            img_size=224,
            patch_size=16,
            pos_embed_rope_base=100,
            pos_embed_rope_min_period=None,
            pos_embed_rope_max_period=None,
            pos_embed_rope_normalize_coords='separate',
            pos_embed_rope_shift_coords=None,
            pos_embed_rope_jitter_coords=None,
            pos_embed_rope_rescale_coords=None,
            qkv_bias=True,
            layerscale_init=1.0e-05,
            norm_layer='layernorm',
            ffn_layer='mlp',
            ffn_bias=True,
            proj_bias=True,
            n_storage_tokens=0,
            mask_k_bias=False,
            untie_cls_and_patch_norms=False,
            untie_global_and_local_cls_norm=False,
            device=device,
        )
        teacher = vits.__dict__['vit_large'](**vit_kwargs)
    else:
        raise NotImplementedError(f'{model_name} is not implemented...')

    # Load checkpoint on CPU to avoid GPU OOM during load, then move model to device
    ckpt = torch.load(ckpt_path, map_location='cpu')['teacher']
    # rename keys
    new_ckpt = {}
    for k, v in ckpt.items():
        if 'backbone' in k:
            k = '.'.join(k.split('.')[1:])
            new_ckpt[k] = v
    msg = teacher.load_state_dict(new_ckpt)
    print(msg)
    # Move model to target device
    teacher.to(device)
    # if gpu_num > 1:
        # teacher = torch.nn.parallel.DataParallel(teacher)
    teacher.eval()
    return teacher, teacher.embed_dim


def build_transform():
    # Use timm's names
    # We prefer input size (512, 512), level 0;
    mean = (0.485, 0.456, 0.406)
    std = (0.229, 0.224, 0.225)

    normalize = transforms.Compose([
        transforms.Resize((224, 224), interpolation=3), 
        transforms.ToTensor(),
        transforms.Normalize(mean=mean, std=std)])
    return normalize