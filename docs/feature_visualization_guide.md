# Feature Map Visualization Guide

This guide explains how to use the feature map visualization tool to analyze and visualize the internal representations learned by different backbone models.

## Overview

The feature map visualization tool (`tools/feature_map_visualization.py`) provides comprehensive visualization capabilities for understanding how different backbone models process cell images. It supports various backbone architectures including ResNet, ViT, DINOv2, and pathology foundation models.

## Features

### 1. Main Feature Map Visualization
- Displays original image alongside feature maps from different layers
- Shows multiple channels with statistical information
- Provides layer-wise statistics and channel variance analysis

### 2. Feature Statistics Analysis
- Channel activation distribution histograms
- Average feature map heatmaps
- Statistical summaries for each layer

### 3. Attention Map Generation
- Creates attention-like visualizations by combining feature maps
- Overlays attention maps on original images
- Helps understand which regions the model focuses on

## Usage

### Basic Usage

```bash
python tools/feature_map_visualization.py \
    --config configs/cell_cls/base_config.yaml \
    --backbone cytofm \
    --dataset All-IDB \
    --gpu 1 \
    --num_samples 5 \
    --num_channels 16 \
    --feature_layer last \
    --output_dir results/feature_maps
```

### Parameters

- `--config`: Path to configuration file (default: `./configs/cell_cls/base_config.yaml`)
- `--backbone`: Backbone model name (e.g., `cytofm`, `ResNet50`, `ViT-B-16`)
- `--dataset`: Dataset name (e.g., `All-IDB`, `SIPaKMeD`, `CERVIX93`)
- `--gpu`: GPU device ID (default: 0)
- `--num_samples`: Number of samples to visualize (default: 5)
- `--num_channels`: Number of feature channels to display (default: 16)
- `--feature_layer`: Which layers to extract (`last`, `intermediate`, `all`)
- `--output_dir`: Output directory for results (default: `./results/feature_maps`)

### Supported Backbone Models

#### Vision Transformers
- `ViT-B-16`, `ViT-B-32`, `ViT-L-14`, `ViT-L-16`, `ViT-L-32`, `ViT-H-14`
- `cytofm` (DINOv3-based)
- `dinov2_vitl`, `gpfm`, `ccs` (DINOv2-based)

#### Convolutional Networks
- `ResNet18`, `ResNet34`, `ResNet50`, `ResNet101`, `ResNet152`

#### Pathology Foundation Models
- `uni`, `uni2`, `conch`, `mstar`, `h-optimus-0`, `h-optimus-1`
- `virchow`, `virchow2`

### Batch Processing

Use the provided shell script for batch processing:

```bash
chmod +x scripts/run_feature_visualization.sh
./scripts/run_feature_visualization.sh
```

This script will run visualizations for multiple backbone models, datasets, and feature layers automatically.

## Output Files

For each sample, the tool generates three types of visualizations:

### 1. Main Visualization (`*_main.png`)
- Original image at the top
- Feature maps from different layers below
- Channel-wise visualizations with statistics
- Layer information and statistics panel

### 2. Feature Statistics (`*_stats.png`)
- Channel activation distribution histograms
- Average feature map heatmaps
- Statistical analysis across layers

### 3. Attention Maps (`*_attention.png`)
- Original image with attention overlays
- Layer-wise attention visualizations
- Helps identify important image regions

### 4. Summary File (`visualization_summary_*.yaml`)
- Metadata about the visualization process
- Feature map shapes and statistics
- File paths and sample information

## Examples

### Visualizing DINOv3 Features

```bash
python tools/feature_map_visualization.py \
    --backbone cytofm \
    --dataset All-IDB \
    --feature_layer all \
    --num_samples 3 \
    --num_channels 12
```

### Comparing Different Backbones

```bash
# ResNet features
python tools/feature_map_visualization.py --backbone ResNet50 --dataset SIPaKMeD

# ViT features  
python tools/feature_map_visualization.py --backbone ViT-B-16 --dataset SIPaKMeD

# DINOv3 features
python tools/feature_map_visualization.py --backbone cytofm --dataset SIPaKMeD
```

### Analyzing Intermediate Layers

```bash
python tools/feature_map_visualization.py \
    --backbone cytofm \
    --dataset All-IDB \
    --feature_layer intermediate \
    --num_channels 20
```

## Interpretation Guide

### Feature Map Patterns

1. **Early Layers**: Usually capture low-level features like edges, textures, and basic shapes
2. **Middle Layers**: Capture more complex patterns and object parts
3. **Late Layers**: Capture high-level semantic features and class-specific patterns

### Attention Maps

- **Bright regions**: Areas the model considers important for classification
- **Dark regions**: Areas the model ignores or considers less relevant
- **Consistent patterns**: Indicate robust feature detection across samples

### Statistical Analysis

- **High variance channels**: Contain discriminative information
- **Low variance channels**: May be less informative or represent background
- **Distribution shapes**: Indicate activation patterns and feature diversity

## Troubleshooting

### Common Issues

1. **GPU Memory Error**: Reduce `num_samples` or `num_channels`
2. **Unsupported Model**: Check if the backbone is in the supported list
3. **Empty Feature Maps**: Some layers might not be compatible with the hook mechanism

### Performance Tips

- Use smaller `num_samples` for initial exploration
- Reduce `num_channels` for faster processing
- Use `feature_layer=last` for quick analysis

## Advanced Usage

### Custom Hook Registration

For models not automatically supported, you can modify the `get_feature_maps` function to register custom hooks for specific layers.

### Custom Visualization

The visualization functions can be imported and used in custom scripts:

```python
from tools.feature_map_visualization import create_visualization, get_feature_maps

# Your custom visualization code here
```

## Contributing

To add support for new backbone models:

1. Update the `get_feature_maps` function with appropriate hook registration
2. Test with different feature layer options
3. Ensure compatibility with the processing pipeline

For questions or issues, please refer to the project documentation or create an issue in the repository.
