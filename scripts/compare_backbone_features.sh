#!/bin/bash

# Compare Backbone Features Script
# This script runs feature visualization for different backbone models and compares them

set -e

# Configuration
IMAGE_DIR="./sample_images"
OUTPUT_BASE_DIR="./results/backbone_comparison"
GPU_ID=1
NUM_SAMPLES=3
NUM_CHANNELS=8

# Create output directory
mkdir -p "$OUTPUT_BASE_DIR"

echo "🔬 Backbone Feature Comparison"
echo "=============================="
echo "Configuration:"
echo "  Image directory: $IMAGE_DIR"
echo "  Output base directory: $OUTPUT_BASE_DIR"
echo "  GPU ID: $GPU_ID"
echo "  Number of samples: $NUM_SAMPLES"
echo "  Number of channels: $NUM_CHANNELS"
echo ""

# Check if sample images exist, if not create them
if [ ! -d "$IMAGE_DIR" ]; then
    echo "📸 Creating sample images..."
    python examples/feature_visualization_example.py
fi

# List of backbone models to compare
BACKBONES=("cytofm" "ccs" "gpfm")

# Function to run feature visualization for a backbone
run_backbone_visualization() {
    local backbone=$1
    local output_dir="$OUTPUT_BASE_DIR/${backbone}_features"
    
    echo "🚀 Running feature visualization for $backbone backbone..."
    echo "   Output directory: $output_dir"
    
    python tools/trained_model_feature_viz.py \
        --backbone "$backbone" \
        --image_dir "$IMAGE_DIR" \
        --gpu "$GPU_ID" \
        --num_samples "$NUM_SAMPLES" \
        --num_channels "$NUM_CHANNELS" \
        --output_dir "$output_dir"
    
    echo "✅ Completed $backbone backbone visualization"
    echo ""
}

# Run visualizations for all backbones
for backbone in "${BACKBONES[@]}"; do
    echo "----------------------------------------"
    run_backbone_visualization "$backbone"
done

# Create comparison summary
echo "📊 Creating comparison summary..."

SUMMARY_FILE="$OUTPUT_BASE_DIR/backbone_comparison_summary.md"

cat > "$SUMMARY_FILE" << EOF
# Backbone Feature Comparison Summary

This document summarizes the feature visualization results for different backbone models.

## Compared Backbones

EOF

for backbone in "${BACKBONES[@]}"; do
    echo "### ${backbone^^} Backbone" >> "$SUMMARY_FILE"
    echo "" >> "$SUMMARY_FILE"
    echo "- **Model Type**: DINOv3-based Vision Transformer" >> "$SUMMARY_FILE"
    echo "- **Feature Extraction**: Patch embedding and transformer blocks" >> "$SUMMARY_FILE"
    echo "- **Visualization Directory**: \`${backbone}_features/\`" >> "$SUMMARY_FILE"
    echo "" >> "$SUMMARY_FILE"
    
    # List generated files
    feature_dir="$OUTPUT_BASE_DIR/${backbone}_features"
    if [ -d "$feature_dir" ]; then
        echo "**Generated Files:**" >> "$SUMMARY_FILE"
        for file in "$feature_dir"/*.png; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                echo "- \`$filename\`" >> "$SUMMARY_FILE"
            fi
        done
        echo "" >> "$SUMMARY_FILE"
    fi
done

cat >> "$SUMMARY_FILE" << EOF

## Analysis Guidelines

### What to Look For

1. **Feature Diversity**: Different backbones may capture different types of features
2. **Spatial Patterns**: How well each backbone preserves spatial information
3. **Channel Activation**: Which channels are most active for different image types
4. **Feature Hierarchy**: How features evolve through different layers

### Comparison Points

- **Patch Embedding**: How each backbone processes initial image patches
- **Feature Complexity**: Complexity and richness of extracted features
- **Spatial Resolution**: How well spatial information is preserved
- **Channel Specialization**: Whether different channels specialize in different features

### Usage

1. Open the visualization files for each backbone
2. Compare the same sample across different backbones
3. Look for differences in feature patterns and activations
4. Analyze which backbone captures the most relevant features for your task

## File Organization

\`\`\`
$OUTPUT_BASE_DIR/
├── cytofm_features/
│   ├── cytofm_sample_001_*.png
│   ├── cytofm_sample_002_*.png
│   └── cytofm_sample_003_*.png
├── ccs_features/
│   ├── ccs_sample_001_*.png
│   ├── ccs_sample_002_*.png
│   └── ccs_sample_003_*.png
├── gpfm_features/
│   ├── gpfm_sample_001_*.png
│   ├── gpfm_sample_002_*.png
│   └── gpfm_sample_003_*.png
└── backbone_comparison_summary.md (this file)
\`\`\`

## Next Steps

1. **Visual Inspection**: Open and compare the generated visualizations
2. **Quantitative Analysis**: Use the feature statistics to compare backbones numerically
3. **Task-Specific Evaluation**: Test which backbone performs best on your specific task
4. **Fine-tuning**: Consider fine-tuning the best-performing backbone for your dataset

EOF

echo "📄 Comparison summary saved to: $SUMMARY_FILE"

# Create a simple HTML viewer for easy comparison
HTML_FILE="$OUTPUT_BASE_DIR/backbone_comparison_viewer.html"

cat > "$HTML_FILE" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backbone Feature Comparison</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .backbone-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .backbone-title {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 15px;
            text-transform: uppercase;
            font-weight: bold;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .image-caption {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            font-weight: bold;
        }
        .comparison-note {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Backbone Feature Comparison</h1>
        
        <div class="comparison-note">
            <strong>How to Use:</strong> Compare the same sample numbers across different backbones to see how each model processes the same input image. Look for differences in feature patterns, spatial preservation, and channel activations.
        </div>

EOF

# Add sections for each backbone
for backbone in "${BACKBONES[@]}"; do
    cat >> "$HTML_FILE" << EOF
        <div class="backbone-section">
            <h2 class="backbone-title">${backbone^^} Backbone</h2>
            <div class="image-grid">
EOF
    
    # Add images for this backbone
    feature_dir="$OUTPUT_BASE_DIR/${backbone}_features"
    if [ -d "$feature_dir" ]; then
        for file in "$feature_dir"/*.png; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                relative_path="${backbone}_features/$filename"
                sample_name=$(echo "$filename" | sed 's/.*_sample_[0-9]*_\(.*\)_features\.png/\1/')
                sample_num=$(echo "$filename" | sed 's/.*_sample_\([0-9]*\)_.*/\1/')
                
                cat >> "$HTML_FILE" << EOF
                <div class="image-item">
                    <img src="$relative_path" alt="$filename">
                    <div class="image-caption">Sample $sample_num: $sample_name</div>
                </div>
EOF
            fi
        done
    fi
    
    cat >> "$HTML_FILE" << EOF
            </div>
        </div>
EOF
done

cat >> "$HTML_FILE" << 'EOF'
    </div>
</body>
</html>
EOF

echo "🌐 HTML viewer created: $HTML_FILE"
echo "   Open this file in a web browser to view the comparison"

echo ""
echo "🎉 Backbone feature comparison completed!"
echo ""
echo "📁 Results saved to: $OUTPUT_BASE_DIR"
echo "📄 Summary: $SUMMARY_FILE"
echo "🌐 HTML Viewer: $HTML_FILE"
echo ""
echo "📊 Generated comparisons for backbones: ${BACKBONES[*]}"
echo ""
echo "Next steps:"
echo "1. Open $HTML_FILE in a web browser"
echo "2. Compare the same sample numbers across different backbones"
echo "3. Analyze feature differences and choose the best backbone for your task"
