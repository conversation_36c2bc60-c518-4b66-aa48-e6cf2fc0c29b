#!/bin/bash

# 定义数据集和对应的 GPU
declare -A DATASET_GPU=(
    ["All-IDB"]=1
    # ["AML"]=1
    # ["Ascites2020"]=2
    # ["Barcelona"]=3
    # ["BCFC"]=4 #
    # ["BCI"]=5
    # ["BMC"]=6 #
    # ["BMT"]=7
    # ["Breast2023"]=7 #
    # ["C_NMC_2019"]=6
    # ["CCS-Cell-Cls"]=0
    # ["CERVIX93"]=4
    # ["CSF2022"]=0
    # ["FNAC2019"]=2
    # ["Herlev"]=0
    # ["HiCervix"]=1
    # ["JinWooChoi"]=0
    # ["LDCC"]=7
    # ["MendeleyLBC"]=1
    # ["PS3C"]=0
    # ["Raabin_WBC"]=1
    # ["RepoMedUNM"]=1
    # ["SIPaKMeD"]=1
    # ["Thyroid2024"]=2
    # ["UFSC_OCPap"]=0
)

# 遍历所有数据集，并使用对应的 GPU 训练
for dataset in "${!DATASET_GPU[@]}"; do
    gpu="${DATASET_GPU[$dataset]}"
    echo "Training dataset $dataset on GPU $gpu"
    sh ./scripts/train_simple.sh -b cell_cls configs/cell_cls/quick_test.yaml \
        common.gpu="$gpu" data.dataset="$dataset"
done

echo "All training jobs submitted!"