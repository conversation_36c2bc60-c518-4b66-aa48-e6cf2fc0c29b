#!/bin/bash

# Feature Map Visualization Script
# This script runs feature map visualization for different backbone models and datasets

set -e

# Configuration
CONFIG_FILE="./configs/cell_cls/base_config.yaml"
OUTPUT_DIR="./results/feature_maps"
GPU_ID=1
NUM_SAMPLES=3
NUM_CHANNELS=12

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "🔬 Starting Feature Map Visualization"
echo "======================================"
echo "Configuration:"
echo "  Config file: $CONFIG_FILE"
echo "  Output directory: $OUTPUT_DIR"
echo "  GPU ID: $GPU_ID"
echo "  Number of samples: $NUM_SAMPLES"
echo "  Number of channels: $NUM_CHANNELS"
echo ""

# Function to run visualization
run_visualization() {
    local backbone=$1
    local dataset=$2
    local feature_layer=$3
    
    echo "🚀 Running visualization for:"
    echo "   Backbone: $backbone"
    echo "   Dataset: $dataset"
    echo "   Feature layer: $feature_layer"
    echo ""
    
    python tools/feature_map_visualization.py \
        --config "$CONFIG_FILE" \
        --backbone "$backbone" \
        --dataset "$dataset" \
        --gpu "$GPU_ID" \
        --num_samples "$NUM_SAMPLES" \
        --num_channels "$NUM_CHANNELS" \
        --feature_layer "$feature_layer" \
        --output_dir "$OUTPUT_DIR"
    
    echo "✅ Completed visualization for $backbone on $dataset"
    echo ""
}

# List of backbone models to test
BACKBONES=("cytofm" "ResNet50" "ViT-B-16")

# List of datasets to test
DATASETS=("All-IDB" "SIPaKMeD")

# List of feature layers to visualize
FEATURE_LAYERS=("last" "intermediate")

# Run visualizations
for backbone in "${BACKBONES[@]}"; do
    for dataset in "${DATASETS[@]}"; do
        for feature_layer in "${FEATURE_LAYERS[@]}"; do
            echo "----------------------------------------"
            run_visualization "$backbone" "$dataset" "$feature_layer"
        done
    done
done

echo "🎉 All feature map visualizations completed!"
echo "📁 Results saved to: $OUTPUT_DIR"
echo ""
echo "📊 Generated files for each sample:"
echo "   - *_main.png: Main feature map visualization"
echo "   - *_stats.png: Feature statistics plots"
echo "   - *_attention.png: Attention maps"
echo "   - visualization_summary_*.yaml: Summary statistics"
